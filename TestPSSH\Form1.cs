﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using PSSHConn;

namespace TestPSSH
{
    public partial class Form1 : Form
    {
        public List<SubAuthentication> subAuthenticationList = null;
        public string shellPrompt = "\\$|#|>";
        public Form1()
        {
            InitializeComponent();
        }

        #region Methods to add in library for rebex login using PSSH
       
        //public static List<PSSHR.SubAuthentication> BindingSubstituteAuth(PODGSSHInfo objSshInfo)
        //{
        //    List<PSSHR.SubAuthentication> subauthlst = new List<PSSHR.SubAuthentication>();

        //    foreach (SubAuthentication v in objSshInfo.SubAuthenticationlist)
        //    {
        //        PSSHR.SubAuthentication psshrsub = new PSSHR.SubAuthentication();

        //        psshrsub.SubAuthenticationType = v.SubAuthenticationType;
        //        psshrsub.SubUser = v.SubUser;
        //        psshrsub.SubPassword = v.SubPassword;
        //        psshrsub.SubPath = v.SubPath;
        //        subauthlst.Add(psshrsub);
        //    }

        //    return subauthlst;
        //}

        //public static PSSHR.SSHServerInfo GetServer(PODGSSHInfo objSSHInfo)
        //{
        //    List<PSSHR.SubAuthentication> SubAuthList = BindingSubstituteAuth(objSSHInfo);
        //    SSHServerInfo PServerInfo = new SSHServerInfo(objSSHInfo.SSHHost, objSSHInfo.SSHUser, objSSHInfo.SSHPass, objSSHInfo.SSHPort, SubAuthList);
        //    return PServerInfo;
        //}

        //public static Rebex.TerminalEmulation.VirtualTerminal BindTerminalToRebexSession(Rebex.Net.Ssh client, SSHServerInfo PServerInfo)
        //{
        //    Rebex.TerminalEmulation.VirtualTerminal session = PSSH.BindTerminalToRebexSession(client);
        //    session = PSSHR.SubAuthentication.ProcessSubAuthentication(PServerInfo, session, shellPrompt);
        //    return session;
        //}

        //public static Rebex.Net.Ssh CreateRebexLogin(SSHServerInfo PServerInfo)
        //{
        //    Rebex.Net.Ssh client = PSSH.CreateRebexSession(PServerInfo);
        //    return client;
        //}

        #endregion Methods to add in library for rebex login using PSSH

        private void btnRun_Click(object sender, EventArgs e)
        {
            try
            {
                List<SubAuthentication> subauthenticationlist=new List<SubAuthentication>();
                SSHServerInfo prssh = new SSHServerInfo(txtPRIP.Text, txtPRUSER.Text, txtPRPASS.Text,Convert.ToInt32(txtport.Text), subauthenticationlist);
               
            }
            catch (Exception _ex)
            {
               // txtoutput.Text = output;
            }
        }

        private void btnconnect_Click(object sender, EventArgs e)
        {
            try
            {
                SSHServerInfo ssh = new SSHServerInfo(txtPRIP.Text, txtPRUSER.Text, txtPRPASS.Text, Convert.ToInt32(txtport.Text), subAuthenticationList);

                // Test the enhanced connection manager
                MessageBox.Show("Testing enhanced SSH connection with retry logic...");

                // Configure connection manager settings for testing
                SSHConnectionManager.MaxRetryAttempts = 3;
                SSHConnectionManager.BaseRetryDelayMs = 1000;
                SSHConnectionManager.MinTimeBetweenConnectionsMs = 500;

                dynamic client = SSHConnectionManager.CreateConnectionWithRetry(ssh, shellPrompt);
                string output = PSSH.ExecuteOSCmmand(ssh, shellPrompt, txtCommand.Text);
                bool result = SSHConnectionManager.SafeDisconnectAndDispose(client, ssh.SSHHost);
                MessageBox.Show("Result: " + result + " Output: " + output);
            }
            catch (CustomExceptions customEx)
            {
                MessageBox.Show("Custom SSH Exception: " + customEx.Message +
                    (customEx.InnerException != null ? "\nInner Exception: " + customEx.InnerException.Message : ""));
            }
            catch(Exception ex)
            {
                // Check if this is a socket buffer error
                if (SSHConnectionManager.IsSocketBufferError(ex))
                {
                    MessageBox.Show("Socket Buffer Error (10055) Detected!\n\n" +
                        "This error typically occurs when:\n" +
                        "1. Too many concurrent connections\n" +
                        "2. Connections not properly cleaned up\n" +
                        "3. System running out of socket buffer space\n\n" +
                        "Error: " + ex.Message + "\n\n" +
                        "The enhanced connection manager will automatically retry with exponential backoff.");
                }
                else
                {
                    MessageBox.Show("Exception: " + ex.Message);
                }
            }
        }

        private void btnTestSocketError_Click(object sender, EventArgs e)
        {
            try
            {
                // Test multiple concurrent connections to potentially trigger socket error 10055
                MessageBox.Show("Testing multiple concurrent connections to simulate socket buffer exhaustion...");

                SSHServerInfo ssh = new SSHServerInfo(txtPRIP.Text, txtPRUSER.Text, txtPRPASS.Text, Convert.ToInt32(txtport.Text), subAuthenticationList);

                List<dynamic> clients = new List<dynamic>();

                // Try to create multiple connections rapidly
                for (int i = 0; i < 10; i++)
                {
                    try
                    {
                        dynamic client = SSHConnectionManager.CreateConnectionWithRetry(ssh, shellPrompt);
                        clients.Add(client);
                        MessageBox.Show($"Connection {i + 1} created successfully");
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Connection {i + 1} failed: {ex.Message}");
                        break;
                    }
                }

                // Clean up all connections
                foreach (var client in clients)
                {
                    SSHConnectionManager.SafeDisconnectAndDispose(client, ssh.SSHHost);
                }

                MessageBox.Show("Test completed. All connections cleaned up.");
            }
            catch (Exception ex)
            {
                MessageBox.Show("Test Exception: " + ex.Message);
            }
        }
    }
}
