<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <appSettings>
    <!-- SSH Connection Flag: JSession or RSession -->
    <add key="SSHConnFlag" value="RSession" />
    
    <!-- Enhanced Connection Manager Settings -->
    
    <!-- Maximum number of retry attempts for failed connections -->
    <add key="SSH_MaxRetryAttempts" value="5" />
    
    <!-- Base delay in milliseconds between retry attempts -->
    <add key="SSH_BaseRetryDelayMs" value="2000" />
    
    <!-- Maximum delay in milliseconds between retry attempts -->
    <add key="SSH_MaxRetryDelayMs" value="30000" />
    
    <!-- Connection timeout in milliseconds -->
    <add key="SSH_ConnectionTimeoutMs" value="60000" />
    
    <!-- Minimum time in milliseconds between connections to the same host -->
    <add key="SSH_MinTimeBetweenConnectionsMs" value="1000" />
    
    <!-- Enable enhanced error logging (true/false) -->
    <add key="SSH_EnableEnhancedLogging" value="true" />
    
    <!-- Enable automatic garbage collection on retry (true/false) -->
    <add key="SSH_EnableGCOnRetry" value="true" />
    
    <!-- Socket error detection sensitivity (1-3, where 3 is most sensitive) -->
    <add key="SSH_SocketErrorSensitivity" value="2" />
  </appSettings>
  
  <!-- Log4Net Configuration for SSH Connection Manager -->
  <log4net>
    <appender name="SSHConnectionFileAppender" type="log4net.Appender.RollingFileAppender">
      <file value="logs\SSHConnection.log" />
      <appendToFile value="true" />
      <rollingStyle value="Size" />
      <maxSizeRollBackups value="10" />
      <maximumFileSize value="10MB" />
      <staticLogFileName value="true" />
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date [%thread] %-5level %logger - %message%newline" />
      </layout>
    </appender>
    
    <logger name="SSHConnectionManager">
      <level value="INFO" />
      <appender-ref ref="SSHConnectionFileAppender" />
    </logger>
    
    <logger name="BcmsEngineLog">
      <level value="INFO" />
      <appender-ref ref="SSHConnectionFileAppender" />
    </logger>
  </log4net>
  
  <!-- Performance Tuning Guidelines -->
  <!--
  
  SOCKET ERROR 10055 TUNING GUIDE:
  
  If you're experiencing frequent socket errors, try these settings:
  
  HIGH VOLUME ENVIRONMENT (Many concurrent connections):
  - SSH_MaxRetryAttempts: 3
  - SSH_BaseRetryDelayMs: 3000
  - SSH_MinTimeBetweenConnectionsMs: 2000
  
  LOW LATENCY ENVIRONMENT (Fast network, few connections):
  - SSH_MaxRetryAttempts: 2
  - SSH_BaseRetryDelayMs: 1000
  - SSH_MinTimeBetweenConnectionsMs: 500
  
  UNRELIABLE NETWORK (Slow or unstable connections):
  - SSH_MaxRetryAttempts: 7
  - SSH_BaseRetryDelayMs: 5000
  - SSH_MaxRetryDelayMs: 60000
  - SSH_ConnectionTimeoutMs: 120000
  
  RESOURCE CONSTRAINED ENVIRONMENT (Limited memory/CPU):
  - SSH_EnableGCOnRetry: true
  - SSH_MinTimeBetweenConnectionsMs: 3000
  - SSH_MaxRetryAttempts: 3
  
  -->
</configuration>
