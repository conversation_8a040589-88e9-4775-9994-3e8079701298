﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using log4net;
using Rebex.Net;
using Rebex.IO;
using Rebex.TerminalEmulation;
using System.Threading;
using System.Text.RegularExpressions;
using System.IO;
using Jscape.Ssh;
using Jscape.Ssh.Transport;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Security;
using Org.BouncyCastle.Crypto.Parameters;
using System.Configuration;

namespace PSSHConn
{
    #region SSHConnFlag
    public enum SSHConnFlag
    {
        //none = 0,
        JSession = 1,
        RSession = 2,
    }
    #endregion SSHConnFlag

    #region SubAuthentication

    public enum SubstituteAuthentication
    {
        //none = 0,
        SudoSu = 1,
        Su = 2,
        Asu = 3,
        Sudo = 4,
        Privrun = 5,
        Other = 6
    }

    public class SubAuthentication
    {
        private static readonly ILog Enginelog = LogManager.GetLogger("BcmsEngineLog");
        public static SSHConnFlag sshConnOpt = (SSHConnFlag)Enum.Parse(typeof(SSHConnFlag), ConfigurationManager.AppSettings["SSHConnFlag"].ToString(), true);

        public int SubAuthenticationType { get; set; }
        public string SubUser { get; set; }
        public string SubPassword { get; set; }
        public string SubPath { get; set; }
        public static string LoginCommand { get; set; }

        public SubAuthentication()
        {
            log4net.Config.XmlConfigurator.Configure();
        }

        public SubAuthentication(int subAuthenticationType, string subUser, string subPassword, string subPath)
        {
            SubAuthenticationType = subAuthenticationType;
            SubUser = subUser;
            SubPassword = subPassword;
            SubPath = subPath;
        }

        public static dynamic ProcessSubAuthentication(SSHServerInfo _server, dynamic _session, String shellPrompt)
        {

            int SubAuthenticationType = 0;
            string command = string.Empty;
            LoginCommand = string.Empty;
            string output = string.Empty;
            Regex _reg = new Regex(shellPrompt);

            if (sshConnOpt == SSHConnFlag.JSession)
            {
                Enginelog.Info(" ProcessSubAuthentication with JSession for ServerIP: " + _server.SSHHost);
                //client = SubAuthentication.ProcessSubAuthentication(_server, _session, shellPrompt);
                if (_server.SubAuthenticationList != null && _server.SubAuthenticationList.Count > 0)
                {
                    foreach (var SubAuthentication in _server.SubAuthenticationList)
                    {
                        SubAuthenticationType = SubAuthentication.SubAuthenticationType;

                        switch (SubAuthentication.SubAuthenticationType)
                        {
                            case (int)SubstituteAuthentication.SudoSu:

                                Enginelog.Info("SUDO SU SubAuthentication Type serverIP: " + _server.SSHHost);

                                command = SubAuthentication.SubPath + "sudo su - " + SubAuthentication.SubUser;
                                Enginelog.Info("serverIP: " + _server.SSHHost + " command: " + command);
                                if (!string.IsNullOrEmpty(SubAuthentication.SubPassword))
                                {
                                    output = _session.SendWait(command, ":", false, 120000);
                                    Enginelog.Info("serverIP: " + _server.SSHHost + " command: " + command + " output: " + output);
                                    _session.SendWait(SubAuthentication.SubPassword, shellPrompt, true, 120000);
                                }
                                else
                                {
                                    output = _session.SendWait(command, shellPrompt, true, 120000);
                                    Enginelog.Info("serverIP: " + _server.SSHHost + " command: " + command + " output: " + output);
                                }
                                Enginelog.Info("SUDO SU SubAuthentication Type serverIP: " + _server.SSHHost + " Completed");
                                break;
                            case (int)SubstituteAuthentication.Su:

                                Enginelog.Info("SU SubAuthentication Type serverIP: " + _server.SSHHost);

                                command = SubAuthentication.SubPath + "su - " + SubAuthentication.SubUser;
                                Enginelog.Info("serverIP: " + _server.SSHHost + " command: " + command);

                                if (!string.IsNullOrEmpty(SubAuthentication.SubPassword))
                                {
                                    output = _session.SendWait(command, ":", false, 120000);
                                    Enginelog.Info("serverIP: " + _server.SSHHost + " command: " + command + " output: " + output);

                                    _session.SendWait(SubAuthentication.SubPassword, shellPrompt, true, 120000);
                                }
                                else
                                {
                                    output = _session.SendWait(command, shellPrompt, true, 120000);
                                    Enginelog.Info("serverIP: " + _server.SSHHost + " command: " + command + " output: " + output);
                                }
                                Enginelog.Info("SU SubAuthentication Type serverIP: " + _server.SSHHost + " Completed");
                                break;
                            case (int)SubstituteAuthentication.Sudo:
                                LoginCommand = SubAuthentication.SubPath + "sudo ";
                                break;

                            case (int)SubstituteAuthentication.Privrun:
                                LoginCommand = SubAuthentication.SubPath + "privrun ";
                                break;

                            case (int)SubstituteAuthentication.Asu:
                                Enginelog.Info("ASU SubAuthentication Type serverIP: " + _server.SSHHost);

                                command = SubAuthentication.SubPath + "asu " + SubAuthentication.SubUser;
                                Enginelog.Info("serverIP: " + _server.SSHHost + " command: " + command);

                                if (!string.IsNullOrEmpty(SubAuthentication.SubPassword))
                                {
                                    output = _session.SendWait(command, ":", false, 120000);
                                    Enginelog.Info("serverIP: " + _server.SSHHost + " command: " + command + " output: " + output);
                                    _session.SendWait(SubAuthentication.SubPassword, shellPrompt, true, 120000);
                                }
                                else
                                {
                                    output = _session.SendWait(command, shellPrompt, true, 120000);
                                    Enginelog.Info("serverIP: " + _server.SSHHost + " command: " + command + " output: " + output);

                                }
                                Enginelog.Info("ASU SubAuthentication Type serverIP: " + _server.SSHHost + " Completed");
                                break;

                            case (int)SubstituteAuthentication.Other:
                                Enginelog.Info("ASU SubAuthentication Type serverIP: " + _server.SSHHost);

                                command = SubAuthentication.SubPath + " " + SubAuthentication.SubUser;
                                Enginelog.Info("serverIP: " + _server.SSHHost + " command: " + command);

                                if (!string.IsNullOrEmpty(SubAuthentication.SubPassword))
                                {
                                    output = _session.SendWait(command, ":", false, 120000);
                                    Enginelog.Info("serverIP: " + _server.SSHHost + " command: " + command + " output: " + output);
                                    _session.SendWait(SubAuthentication.SubPassword, shellPrompt, true, 120000);
                                }
                                else
                                {
                                    output = _session.SendWait(command, shellPrompt, true, 120000);
                                    Enginelog.Info("serverIP: " + _server.SSHHost + " command: " + command + " output: " + output);

                                }
                                Enginelog.Info("ASU SubAuthentication Type serverIP: " + _server.SSHHost + " Completed");
                                break;
                        }
                    }
                }
                return _session;
            }
            else
            {
                Enginelog.Info(" ProcessSubAuthentication with RSession for ServerIP: " + _server.SSHHost);
                VirtualTerminal terminal = null;
                //terminal.Bind(_session);
                terminal = _session;
                Thread.Sleep(1000);
                //client = SubAuthentication.ProcessSubAuthentication(__server, terminal, shellPrompt);
                if (_server.SubAuthenticationList != null && _server.SubAuthenticationList.Count > 0)
                {
                    foreach (var SubAuthentication in _server.SubAuthenticationList)
                    {
                        SubAuthenticationType = SubAuthentication.SubAuthenticationType;


                        switch (SubAuthentication.SubAuthenticationType)
                        {
                            case (int)SubstituteAuthentication.SudoSu:

                                command = SubAuthentication.SubPath + "sudo su - " + SubAuthentication.SubUser;
                                Enginelog.Info(" PSSH : SUDO SU SubAuthentication Type _serverIP: " + _server.SSHHost + " command: " + command);

                                if (!string.IsNullOrEmpty(SubAuthentication.SubPassword))
                                {
                                    terminal.Process();
                                    terminal.SendToServer(command + "\r\n");
                                    terminal.Expect(":", 10000);
                                    output = terminal.ReceivedData;
                                    Enginelog.Info(" PSSH : SUDO SU SubAuthentication Type serverIP:" + _server.SSHHost + " command: " + command + " output: " + output);

                                    terminal.Process();
                                    terminal.SendToServer(SubAuthentication.SubPassword + "\r\n");
                                    terminal.Expect(_reg, 10000);
                                    output = terminal.ReceivedData;
                                }
                                else
                                {
                                    terminal.Process();
                                    terminal.SendToServer(command + "\r\n");
                                    terminal.Expect(_reg, 10000);
                                    output = terminal.ReceivedData;

                                    Enginelog.Info(" PSSH : SUDO SU SubAuthentication Type _serverIP:" + _server.SSHHost + " command: " + command + " output: " + output);
                                }
                                Enginelog.Info(" SUDO SU SubAuthentication Type _serverIP: " + _server.SSHHost + " Completed");
                                break;
                            case (int)SubstituteAuthentication.Su:

                                command = SubAuthentication.SubPath + "su - " + SubAuthentication.SubUser;
                                Enginelog.Info(" PSSH : SU SubAuthentication Type _serverIP: " + _server.SSHHost + " command: " + command);

                                if (!string.IsNullOrEmpty(SubAuthentication.SubPassword))
                                {
                                    terminal.Process();
                                    terminal.SendToServer(command + "\r\n");
                                    terminal.Expect(":", 10000);
                                    output = terminal.ReceivedData;

                                    Enginelog.Info(" PSSH : SU SubAuthentication Type serverIP:" + _server.SSHHost + " command: " + command + " output: " + output);


                                    terminal.Process();
                                    terminal.SendToServer(SubAuthentication.SubPassword + "\r\n");
                                    terminal.Expect(_reg, 10000);
                                    output = terminal.ReceivedData;
                                }
                                else
                                {
                                    terminal.Process();
                                    terminal.SendToServer(command + "\r\n");
                                    terminal.Expect(_reg, 10000);
                                    output = terminal.ReceivedData;

                                    Enginelog.Info(" PSSH : SU SubAuthentication Type serverIP:" + _server.SSHHost + " command: " + command + " output: " + output);
                                }
                                Enginelog.Info(" SSH SU SubAuthentication Type _serverIP: " + _server.SSHHost + " Completed");
                                break;
                            case (int)SubstituteAuthentication.Sudo:
                                LoginCommand = SubAuthentication.SubPath + "sudo ";
                                break;

                            case (int)SubstituteAuthentication.Privrun:
                                LoginCommand = SubAuthentication.SubPath + "privrun ";
                                break;

                            case (int)SubstituteAuthentication.Asu:

                                command = SubAuthentication.SubPath + "asu " + SubAuthentication.SubUser;
                                Enginelog.Info(" PSSH : ASU SubAuthentication Type _serverIP: " + _server.SSHHost + " command: " + command);

                                if (!string.IsNullOrEmpty(SubAuthentication.SubPassword))
                                {
                                    terminal.Process();
                                    terminal.SendToServer(command + "\r\n");
                                    terminal.Expect(":", 10000);
                                    output = terminal.ReceivedData;

                                    Enginelog.Info(" PSSH  : ASU SubAuthentication Type serverIP: " + _server.SSHHost + " command: " + command + " output: " + output);
                                    terminal.Process();
                                    terminal.SendToServer(SubAuthentication.SubPassword + "\r\n");
                                    terminal.Expect(_reg, 10000);
                                    output = terminal.ReceivedData;
                                }
                                else
                                {
                                    terminal.Process();
                                    terminal.SendToServer(command + "\r\n");
                                    terminal.Expect(_reg, 10000);
                                    output = terminal.ReceivedData;

                                    Enginelog.Info(" PSSH : ASU SubAuthentication Type serverIP: " + _server.SSHHost + " command: " + command + " output: " + output);

                                }
                                Enginelog.Info(" PSSH : ASU SubAuthentication Type _serverIP: " + _server.SSHHost + " Completed");
                                break;

                            case (int)SubstituteAuthentication.Other:

                                command = SubAuthentication.SubPath + " " + SubAuthentication.SubUser;
                                Enginelog.Info(" PSSH : Other SubAuthentication Type _serverIP: " + _server.SSHHost + " command: " + command);

                                if (!string.IsNullOrEmpty(SubAuthentication.SubPassword))
                                {
                                    terminal.Process();
                                    terminal.SendToServer(command + "\r\n");
                                    terminal.Expect(":", 10000);
                                    output = terminal.ReceivedData;

                                    Enginelog.Info(" PSSH : Other SubAuthentication Type serverIP: " + _server.SSHHost + " command: " + command + " output: " + output);
                                    terminal.Process();
                                    terminal.SendToServer(SubAuthentication.SubPassword + "\r\n");
                                    terminal.Expect(_reg, 10000);
                                    output = terminal.ReceivedData;
                                }
                                else
                                {
                                    terminal.Process();
                                    terminal.SendToServer(command + "\r\n");
                                    terminal.Expect(_reg, 10000);
                                    output = terminal.ReceivedData;

                                    Enginelog.Info(" PSSH : Other SubAuthentication Type serverIP: " + _server.SSHHost + " command: " + command + " output: " + output);

                                }
                                Enginelog.Info(" PSSH : Other SubAuthentication Type _serverIP: " + _server.SSHHost + " Completed");
                                break;

                        }
                    }
                }
                return terminal;
            }
        }

    }

    #endregion SubAuthentication

    #region SSHServerInfo

    public class SSHServerInfo
    {
        public string SSHHost { get; set; }
        public string SSHUser { get; set; }
        public string SSHPass { get; set; }
        public bool AuthKey { get; set; }
        public string SSHKeyPath { get; set; }
        public string SSHKeyPassword { get; set; }
        public List<SubAuthentication> SubAuthenticationList = null;
        private int _port;
        public string SudoUser { get; set; }

        public bool IsASMGrid { get; set; }
        public string GridHomePath { get; set; }
        public string GridInstanceName { get; set; }
        public string GridUserName { get; set; }
        public string GridPassword { get; set; }

        public int SSHPort
        {

            get { return _port; }
            set
            {
                if (value == 0)
                {
                    value = 22;
                }
                _port = value;
            }
        }

        public List<SubAuthentication> _subauthenticationlist = new List<SubAuthentication>();

        public List<SubAuthentication> SubAuthenticationlist
        {
            get { return _subauthenticationlist; }
            set { _subauthenticationlist = value; }
        }

        public SSHServerInfo()
        {

        }

        public SSHServerInfo(string strSSHHost, string strSSHUser, string strSSHPass)
        {
            SSHHost = strSSHHost;
            SSHUser = strSSHUser;
            SSHPass = strSSHPass;
        }

        // without sshkey
        public SSHServerInfo(string strSSHHost, string strSSHUser, string strSSHPass, int strsshport, List<SubAuthentication> subauthenticationlist)
        {
            SSHHost = strSSHHost;
            SSHUser = strSSHUser;
            SSHPass = strSSHPass;
            AuthKey = false;
            SSHPort = strsshport;
            SubAuthenticationList = subauthenticationlist;
        }

        // with sshkey
        public SSHServerInfo(string strSSHHost, string strSSHUser, string strSSHKeyPath, string strSSHKeyPassword, int strsshport, List<SubAuthentication> subauthenticationlist)
        {
            SSHHost = strSSHHost;
            SSHUser = strSSHUser;
            AuthKey = true;
            SSHKeyPath = strSSHKeyPath;
            SSHKeyPassword = strSSHKeyPassword;
            SSHPort = strsshport;
            SubAuthenticationList = subauthenticationlist;
        }

        public SSHServerInfo(string strSSHHost, string strSSHUser, string strSSHPass, bool authkey, string strSSHKeyPath, string strSSHKeyPassword, int strsshport)
        {
            SSHHost = strSSHHost;
            SSHUser = strSSHUser;
            SSHPass = strSSHPass;
            AuthKey = authkey;
            SSHKeyPath = strSSHKeyPath;
            SSHKeyPassword = strSSHKeyPassword;
            SSHPort = strsshport;
        }

        public SSHServerInfo(string strSSHHost, string strSSHUser, string strSSHPass, bool authkey, string strSSHKeyPath, string strSSHKeyPassword, int strsshport, List<SubAuthentication> subauthenticationlist)
        {
            SSHHost = strSSHHost;
            SSHUser = strSSHUser;
            SSHPass = strSSHPass;
            AuthKey = authkey;
            SSHKeyPath = strSSHKeyPath;
            SSHKeyPassword = strSSHKeyPassword;
            SSHPort = strsshport;
            SubAuthenticationList = subauthenticationlist;
        }
    }

    #endregion SSHServerInfo

    #region SSH - bouncy castle

    public class SshUtil
    {
        public static SshConfiguration GetSshConfig()
        {
            SshConfiguration sshConfig = new SshConfiguration();
            sshConfig.ConnectionConfig.TransportConfiguration.RemoveAllCiphers();
            sshConfig.ConnectionConfig.TransportConfiguration.AddCipher(new BouncyCastleCipher.Creator("AES", "ctr", "aes128-ctr", 16, 16));
            return sshConfig;
        }
    }

    public class BouncyCastleCipher : ICipher
    {
        private readonly IBufferedCipher cipher;

        public BouncyCastleCipher(IBufferedCipher cipher)
        {
            this.cipher = cipher;
        }

        /// <summary>
        /// The cipher block length.
        /// </summary>
        public int BlockLength
        {
            get { return this.cipher.GetBlockSize(); }
        }

        /// <summary>
        /// Processes the specified data.
        /// </summary>
        /// <param name="data">the data to process</param>
        /// <returns>the processed data</returns>
        public byte[] Process(byte[] data)
        {
            return this.cipher.ProcessBytes(data);
        }

        public class Creator : ICipherCreator
        {
            private readonly string algorithmName;
            private readonly string mode;
            private readonly string sshAlgorithmName;
            private readonly int keyLength;
            private readonly int blockLength;

            public Creator(string algorithmName, string mode, string sshAlgorithmName, int keyLength, int blockLength)
            {

                this.algorithmName = algorithmName;
                this.mode = mode;
                this.sshAlgorithmName = sshAlgorithmName;
                this.keyLength = keyLength;
                this.blockLength = blockLength;
            }

            /// <summary>
            /// The cipher key length.
            /// </summary>
            public int KeyLength
            {
                get { return this.keyLength; }
            }

            /// <summary>
            /// The cipher block length.
            /// </summary>
            public int BlockLength
            {
                get { return this.blockLength; }
            }

            /// <summary>
            /// Creates a new encipher instance.
            /// </summary>
            /// <param name="keyData">the cipher key data</param>
            /// <param name="ivData">the cipher IV data</param>
            /// <returns>the created encipher</returns>
            public ICipher CreateEncipher(byte[] keyData, byte[] ivData)
            {
                IBufferedCipher cipher = CipherFor(keyData, ivData, true);
                return new BouncyCastleCipher(cipher);
            }

            /// <summary>
            /// Creates a new decipher instance.
            /// </summary>
            /// <param name="keyData">the cipher key data</param>
            /// <param name="ivData">the cipher IV data</param>
            /// <returns>the created decipher</returns>
            public ICipher CreateDecipher(byte[] keyData, byte[] ivData)
            {
                IBufferedCipher cipher = CipherFor(keyData, ivData, false);
                return new BouncyCastleCipher(cipher);
            }

            /// <summary>
            /// The creator algorithm name.
            /// </summary>
            public string Name
            {
                get { return this.sshAlgorithmName; }
            }

            private IBufferedCipher CipherFor(byte[] keyData, byte[] ivData, bool encryption)
            {
                string cipherAlgorithm = this.algorithmName + "/" + this.mode + "/NoPadding";

                IBufferedCipher cipher = CipherUtilities.GetCipher(cipherAlgorithm);
                KeyParameter keyParameter = ParameterUtilities.CreateKeyParameter(this.algorithmName, keyData);
                ParametersWithIV parameters = new ParametersWithIV(keyParameter, ivData);
                cipher.Init(encryption, parameters);

                return cipher;
            }

        }

    }

    #endregion SSH - bouncy castle

    #region PSSH

    public class PSSH
    {
        #region Variables

        private static readonly ILog Enginelog = LogManager.GetLogger("BcmsEngineLog");

        public static string shellPrompt = "\\$|#|>";
        public static int expectTime = 1200000;
        public static Regex _reg = new Regex(shellPrompt);

        public static SSHConnFlag sshConnOpt = (SSHConnFlag)Enum.Parse(typeof(SSHConnFlag), ConfigurationManager.AppSettings["SSHConnFlag"].ToString(), true);

        #endregion Variables

        #region Session - PSSH

        #region Create/Disconnect Session Methods - PSSH

        private static Jscape.Ssh.SshParameters BindSSHParameters(SSHServerInfo _remoteServer)
        {
            string keyPass = string.Empty;
            Jscape.Ssh.SshParameters _sp = new Jscape.Ssh.SshParameters();
            _sp.Hostname = _remoteServer.SSHHost;
            _sp.Username = _remoteServer.SSHUser;
            _sp.Port = _remoteServer.SSHPort;
            if (_remoteServer.AuthKey)
            {
                FileInfo _pvtKey = new FileInfo(_remoteServer.SSHKeyPath);
                if (!string.IsNullOrEmpty(_remoteServer.SSHKeyPassword))
                {
                    keyPass = _remoteServer.SSHKeyPassword;
                }
                _sp.SetPrivateKey(_pvtKey, keyPass);
                Enginelog.Info("PSSH: JSSH Parameters with SSHKey created sucessfully..." + _remoteServer.SSHHost);
            }
            else
            {
                _sp.Password = _remoteServer.SSHPass;
                Enginelog.Info("PSSH: JSSH Parameters created sucessfully..." + _remoteServer.SSHHost);
            }

            return _sp;
        }

        public static dynamic CreateSSHSession(SSHServerInfo objSshInfo)
        {
            string strOut = string.Empty;
            dynamic client = null;
            VirtualTerminal terminal = null;
            if (objSshInfo != null)
            {
                try
                {
                    switch (sshConnOpt)
                    {
                        case SSHConnFlag.JSession:
                            client = new Jscape.Ssh.Ssh();
                            break;
                        case SSHConnFlag.RSession:
                            client = new Rebex.Net.Ssh();
                            Rebex.Security.Cryptography.CryptoHelper.UseFipsAlgorithmsOnly = false;
                            client.Timeout = 3600000;
                            break;
                    }

                    Enginelog.Info("PSSH: Creating SSH Session... " + objSshInfo.SSHHost);
                    if (sshConnOpt == SSHConnFlag.JSession)
                    {
                        client = new Jscape.Ssh.SshSession(BindSSHParameters(objSshInfo), SshUtil.GetSshConfig());
                        client.SetShellPrompt(shellPrompt, true);

                        client.LicenseKey =
                            "SSH Factory for .NET:Single Developer:Registered User:01-01-3999:SqmHLZPj8UhKl9+/RAnmAkXkCPfv7c3fH2F1bKjYwTmH4pOyXv5shXQkj3dAJ5Njrxa9BfTEQJmrQrccH5/vc6zXuqnBN8IOzGFjKu1V7ma7M0F54tYLuMuthW6FIoPjLFFAUU6G0LEF3rSUYDWO3vb2hSWDtFOGW87lGiNE5AA=";
                        client.Connect(300000);
                        Enginelog.Info("PSSH: JSession created sucessfully..." + objSshInfo.SSHHost);
                    }
                    else if (sshConnOpt == SSHConnFlag.RSession)
                    {
                        Rebex.Security.Cryptography.CryptoHelper.UseFipsAlgorithmsOnly = false;
                        client.Timeout = 3600000;
                        if (objSshInfo.SSHPort != 0)
                        {
                            client.Connect(objSshInfo.SSHHost, objSshInfo.SSHPort);
                        }
                        else
                        {
                            client.Connect(objSshInfo.SSHHost);
                        }

                        if (objSshInfo.AuthKey)
                        {
                            SshPrivateKey _pvtkey1 = new SshPrivateKey(objSshInfo.SSHKeyPath, objSshInfo.SSHKeyPassword);
                            client.Login(objSshInfo.SSHUser, _pvtkey1);
                        }
                        else
                        {
                            client.Login(objSshInfo.SSHUser, objSshInfo.SSHPass);
                        }

                        terminal = new VirtualTerminal(80, 400);
                        terminal.Bind(client);
                        client = terminal;
                        Enginelog.Info("PSSH: RSession created sucessfully..." + objSshInfo.SSHHost);
                    }
                }
                catch (Jscape.Ssh.SshException SshEx)
                {
                    if (SshEx.InnerException != null)
                        Enginelog.Error("JPSSH: INNER_SSHException: While connecting to server: " + objSshInfo.SSHHost + " : Messege: " + SshEx.InnerException.ToString());

                    Enginelog.Error("JPSSH: SSHException: While connecting to server: " + objSshInfo.SSHHost + " : Messege: " + SshEx.Message);

                    throw SshEx;
                }
                catch (Rebex.Net.SshException SshEx)
                {
                    if (SshEx.InnerException != null)
                        Enginelog.Error("RPSSH: INNER_SSHException: While connecting to server: " + objSshInfo.SSHHost + ": Messege: " + SshEx.InnerException.ToString());

                    Enginelog.Error("RPSSH: SSHException: While connecting to server: " + objSshInfo.SSHHost + ": Messege: " + SshEx.Message);

                    throw SshEx;
                }
                catch (Exception exc)
                {
                    if (exc.InnerException != null)
                        Enginelog.Error("PSSH: INNER_Exception: While connecting to server: " + objSshInfo.SSHHost + ": Messege: " + exc.InnerException.Message.ToString());

                    Enginelog.Error("PSSH: Exception: While connecting to server: " + objSshInfo.SSHHost + ": Messege: " + exc.Message);

                    throw exc;
                }
            }
            else
            {
                Enginelog.Error("PSSH: SSHSERVER NULL While Create SSH Session for SSHFlag: " + sshConnOpt.ToString());
            }
            return client;
        }

        public static bool DisconnectAndRemoveSSHSession(dynamic client)
        {
            bool result = false;

            if (client != null)
            {
                try
                {
                    switch (sshConnOpt)
                    {
                        case SSHConnFlag.JSession:
                            {
                                client.Disconnect();
                            }
                            break;
                        case SSHConnFlag.RSession:
                            {
                                //if (client.IsConnected)
                                //{
                                //    client.Disconnect();
                                //}
                                //if (!client.IsDisposed)
                                //{
                                //    client.Dispose();
                                //}
                                if (client != null)
                                {
                                    client.Unbind();
                                }
                                if (!client.IsDisposed)
                                {
                                    client.Dispose();
                                }
                                //client = new Rebex.Net.Ssh();
                            }
                            break;
                    }

                    result = true;
                }
                catch (Jscape.Ssh.SshException SshEx)
                {
                    if (SshEx.InnerException != null)
                        Enginelog.Error("JPSSH: INNER_SSHException: on DisconnectAndRemoveSSHSession: Jscape SshEx InnerException: " + SshEx.InnerException.ToString());

                    Enginelog.Error("JPSSH: SSHException: on DisconnectAndRemoveSSHSession: Jscape SshEx Exception: " + SshEx.Message);
                }
                catch (Rebex.Net.SshException SshEx)
                {
                    if (SshEx.InnerException != null)
                        Enginelog.Error("RPSSH: INNER_SSHException: on DisconnectAndRemoveSSHSession: Rebex SshEx InnerException: " + SshEx.InnerException.ToString());

                    Enginelog.Error("RPSSH: SSHException: on DisconnectAndRemoveSSHSession: Rebex SshEx Exception: " + SshEx.Message);
                }
                catch (Exception exc)
                {
                    if (exc.InnerException != null)
                        Enginelog.Error("PSSH: INNER_Exception: on DisconnectAndRemoveSSHSession:  InnerException: " + exc.InnerException.Message.ToString());

                    Enginelog.Error("PSSH: Exception: on DisconnectAndRemoveSSHSession: Exception: " + exc.Message);
                }
            }
            else
            {
                Enginelog.Error("PSSH: client is null on DisconnectAndRemoveSSHSession for: " + sshConnOpt.ToString());
            }
            return result;
        }

        #endregion Create Session - PSSH

        #region Common Execution methods - PSSH

        public static string ExecuteOSCmmand(SSHServerInfo objSshInfo, string shellPrompt, string command)
        {
            string strOut = string.Empty;
            strOut = ExecuteOSCmmand(objSshInfo, shellPrompt, command, expectTime);
            return strOut;
        }

        public static string ExecuteOSCmmand(SSHServerInfo objSshInfo, string shellPrompt, string command, int timeout)
        {
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrompt);
            dynamic client = null;
            VirtualTerminal terminal = null;
            try
            {
                client = CreateSSHSession(objSshInfo);

                if (client != null)
                {
                    if (sshConnOpt == SSHConnFlag.JSession)
                    {
                        client.SetShellPrompt(shellPrompt, true);
                        strOut = client.SendWait(command, shellPrompt, true, timeout);                        
                    }
                    else if (sshConnOpt == SSHConnFlag.RSession)
                    {
                        //terminal = new VirtualTerminal(80, 400);
                        //terminal.Bind(client);

                        terminal = client;

                        terminal.Process();
                        Thread.Sleep(1000);
                        terminal.SendToServer(command + "\n");
                        terminal.Expect(_reg, timeout);
                        Thread.Sleep(3000);
                        strOut = terminal.ReceivedData;
                    }
                    Enginelog.Info("PSSH: Command(in ExecuteOSCmmand): " + command + " executed successfully");
                }

            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                {
                    Enginelog.Error("PSSH: While ExecuteOSCmmand(with server conn details), InnerException: " + exc.InnerException.Message.ToString());
                }
                else
                {
                    Enginelog.Error("PSSH: While ExecuteOSCmmand(with server conn details), Exception: " + exc.Message.ToString());
                }
            }
            finally
            {
                if (sshConnOpt == SSHConnFlag.RSession)
                {
                    if (terminal != null)
                    {
                        terminal.Unbind();
                    }
                    if (!terminal.IsDisposed)
                    {
                        terminal.Dispose();
                    }
                }
                DisconnectAndRemoveSSHSession(client);
            }

            return strOut;
        }

        public static string ExecuteOSCommandWithSubAuth(SSHServerInfo objSshInfo, string shellPrompt, string command)
        {
            string strOut = string.Empty;
            strOut = ExecuteOSCommandWithSubAuth(objSshInfo, shellPrompt, command, expectTime);
            return strOut;
        }

        public static string ExecuteOSCommandWithSubAuth(SSHServerInfo objSshInfo, string shellPrompt, string command, int timeout)
        {
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrompt);
            dynamic client = null;
            VirtualTerminal terminal = null;
            try
            {
                client = CreateSSHSession(objSshInfo);

                if (client != null)
                {
                    client = SubAuthentication.ProcessSubAuthentication(objSshInfo, client, shellPrompt);

                    if (sshConnOpt == SSHConnFlag.JSession)
                    {
                        client.SetShellPrompt(shellPrompt, true);
                        strOut = client.SendWait(command, shellPrompt, true, timeout);
                    }
                    else if (sshConnOpt == SSHConnFlag.RSession)
                    {
                        //terminal = new VirtualTerminal(80, 400);
                        //terminal.Bind(client);
                        terminal = client;

                        terminal.Process();
                        Thread.Sleep(1000);
                        terminal.SendToServer(command + "\n");
                        terminal.Expect(_reg, timeout);
                        Thread.Sleep(3000);
                        strOut = terminal.ReceivedData;
                    }
                    Enginelog.Info("PSSH: Command(in ExecuteOSCmmand): " + command + " executed successfully");
                }

            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                {
                    Enginelog.Error("PSSH: While ExecuteOSCmmand(with server conn details), InnerException: " + exc.InnerException.Message.ToString());
                }
                else
                {
                    Enginelog.Error("PSSH: While ExecuteOSCmmand(with server conn details), Exception: " + exc.Message.ToString());
                }
            }
            finally
            {
                if (sshConnOpt == SSHConnFlag.RSession)
                {
                    if (terminal != null)
                    {
                        terminal.Unbind();
                    }
                    if (!terminal.IsDisposed)
                    {
                        terminal.Dispose();
                    }
                }
                DisconnectAndRemoveSSHSession(client);
            }

            return strOut;
        }

        public static string ExecuteOSCommands(SSHServerInfo objSshInfo, string shellPrompt, string[] commands)
        {
            string strOut = string.Empty;
            strOut = ExecuteOSCommands(objSshInfo, shellPrompt, commands, expectTime);
            return strOut;
        }

        public static string ExecuteOSCommands(SSHServerInfo objSshInfo, string shellPrompt, string[] commands, int timeout)
        {
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrompt);
            dynamic client = null;
            VirtualTerminal terminal = null;
            try
            {
                client = CreateSSHSession(objSshInfo);

                if (client != null)
                {
                    for (int i = 0; i < commands.Length; i++)
                    {
                        string command = commands[i];

                        if (sshConnOpt == SSHConnFlag.JSession)
                        {
                            client.SetShellPrompt(shellPrompt, true);
                            strOut = client.SendWait(command, shellPrompt, true, timeout);
                        }
                        else if (sshConnOpt == SSHConnFlag.RSession)
                        {
                            //terminal = new VirtualTerminal(80, 400);
                            //terminal.Bind(client);

                            terminal = client;

                            terminal.Process();
                            Thread.Sleep(1000);
                            terminal.SendToServer(command + "\n");
                            terminal.Expect(_reg, timeout);
                            Thread.Sleep(3000);
                            strOut = terminal.ReceivedData;
                        }
                        Enginelog.Info("PSSH: Command(in ExecuteOSCommands): " + command + " executed successfully");
                    }
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                {
                    Enginelog.Error("PSSH: While ExecuteOSCommands(with server conn details), InnerException: " + exc.InnerException.Message.ToString());
                }
                else
                {
                    Enginelog.Error("PSSH: While ExecuteOSCommands(with server conn details), Exception: " + exc.Message.ToString());
                }
            }
            finally
            {
                if (sshConnOpt == SSHConnFlag.RSession)
                {
                    if (terminal != null)
                    {
                        terminal.Unbind();
                    }
                    if (!terminal.IsDisposed)
                    {
                        terminal.Dispose();
                    }
                }
                DisconnectAndRemoveSSHSession(client);
            }

            return strOut;
        }

        public static string ExecuteOSCommandsWithSubAuth(SSHServerInfo objSshInfo, string shellPrompt, string[] commands)
        {
            string strOut = string.Empty;
            strOut = ExecuteOSCommandsWithSubAuth(objSshInfo, shellPrompt, commands, expectTime);
            return strOut;
        }

        public static string ExecuteOSCommandsWithSubAuth(SSHServerInfo objSshInfo, string shellPrompt, string[] commands, int timeout)
        {
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrompt);
            dynamic client = null;
            VirtualTerminal terminal = null;
            try
            {
                client = CreateSSHSession(objSshInfo);

                if (client != null)
                {

                    client = SubAuthentication.ProcessSubAuthentication(objSshInfo, client, shellPrompt);

                    for (int i = 0; i < commands.Length; i++)
                    {
                        string command = commands[i];

                        if (sshConnOpt == SSHConnFlag.JSession)
                        {
                            client.SetShellPrompt(shellPrompt, true);
                            strOut = client.SendWait(command, shellPrompt, true, timeout);
                        }
                        else if (sshConnOpt == SSHConnFlag.RSession)
                        {
                            //terminal = new VirtualTerminal(80, 400);
                            //terminal.Bind(client);
                            terminal = client;

                            terminal.Process();
                            Thread.Sleep(1000);
                            terminal.SendToServer(command + "\n");
                            terminal.Expect(_reg, timeout);
                            Thread.Sleep(3000);
                            strOut = terminal.ReceivedData;
                        }
                        Enginelog.Info("PSSH: Command(in ExecuteOSCommandsWithSubAuth): " + command + " executed successfully");
                    }
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                {
                    Enginelog.Error("PSSH: While ExecuteOSCommandsWithSubAuth(with server conn details), InnerException: " + exc.InnerException.Message.ToString());
                }
                else
                {
                    Enginelog.Error("PSSH: While ExecuteOSCommandsWithSubAuth(with server conn details), Exception: " + exc.Message.ToString());
                }
            }
            finally
            {
                if (sshConnOpt == SSHConnFlag.RSession)
                {
                    if (terminal != null)
                    {
                        terminal.Unbind();
                    }
                    if (!terminal.IsDisposed)
                    {
                        terminal.Dispose();
                    }
                }
                DisconnectAndRemoveSSHSession(client);
            }

            return strOut;
        }

        public static string ExecuteOSCmmandWithSession(dynamic client, string shellPrompt, string command)
        {
            string strOut = string.Empty;
            strOut = ExecuteOSCmmandWithSession(client, shellPrompt, command, expectTime);
            return strOut;
        }

        public static string ExecuteOSCmmandWithSession(dynamic client, string shellPrompt, string command, int timeout)
        {
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrompt);
            VirtualTerminal terminal = null;
            try
            {
                if (client != null)
                {
                    if (sshConnOpt == SSHConnFlag.JSession)
                    {
                        client.SetShellPrompt(shellPrompt, true);
                        strOut = client.SendWait(command, shellPrompt, true, timeout);
                    }
                    else if (sshConnOpt == SSHConnFlag.RSession)
                    {
                        //terminal = new VirtualTerminal(80, 400);
                        //terminal.Bind(client);

                        terminal = client;

                        terminal.Process();
                        Thread.Sleep(1000);
                        terminal.SendToServer(command + "\n");
                        terminal.Expect(_reg, timeout);
                        Thread.Sleep(3000);
                        strOut = terminal.ReceivedData;
                    }
                }
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                {
                    Enginelog.Error("PSSH: While ExecuteOSCmmand(with dynamic conn object), InnerException: " + exc.InnerException.Message.ToString());
                }
                else
                {
                    Enginelog.Error("PSSH: While ExecuteOSCmmand(with dynamic conn object), Exception: " + exc.Message.ToString());
                }
            }

            //finally
            //{
            //    if (sshConnOpt == SSHConnFlag.RSession)
            //    {
            //        if (terminal != null)
            //        {
            //            terminal.Unbind();
            //        }
            //        if (!terminal.IsDisposed)
            //        {
            //            terminal.Dispose();
            //        }
            //    }
            //}

            return strOut;
        }

        //public static string ExecuteOSCmmandWithSession(dynamic client, string shellPrompt, string command, bool optTerminal)
        //{
        //    string strOut = string.Empty;
        //    strOut = ExecuteOSCmmandWithSession(client, shellPrompt, command, expectTime, optTerminal);
        //    return strOut;
        //}

        //public static string ExecuteOSCmmandWithSession(dynamic client, string shellPrompt, string command, int timeout, bool optTerminal)
        //{
        //    string strOut = string.Empty;
        //    Regex _reg = new Regex(shellPrompt);
        //    VirtualTerminal terminal = null;
        //    try
        //    {
        //        if (client != null)
        //        {
        //            if (sshConnOpt == SSHConnFlag.JSession)
        //            {
        //                client.SetShellPrompt(shellPrompt, true);
        //                strOut = client.SendWait(command, shellPrompt, true, timeout);
        //            }
        //            else if (sshConnOpt == SSHConnFlag.RSession)
        //            {                        
        //                if (optTerminal)
        //                {
        //                    terminal = new VirtualTerminal(80, 400);
        //                    terminal.Bind(client);
        //                }
        //                else
        //                {
        //                    terminal = client;
        //                }

        //                terminal.Process();
        //                Thread.Sleep(1000);
        //                terminal.SendToServer(command + "\n");
        //                terminal.Expect(_reg, timeout);
        //                Thread.Sleep(3000);
        //                strOut = terminal.ReceivedData;
        //            }
        //        }
        //    }
        //    catch (Exception exc)
        //    {
        //        if (exc.InnerException != null)
        //        {
        //            Enginelog.Error("PSSH: While ExecuteOSCmmand(with dynamic conn object), InnerException: " + exc.InnerException.Message.ToString());
        //        }
        //        else
        //        {
        //            Enginelog.Error("PSSH: While ExecuteOSCmmand(with dynamic conn object), Exception: " + exc.Message.ToString());
        //        }
        //    }
        //    finally
        //    {
        //        if (sshConnOpt == SSHConnFlag.RSession)
        //        {
        //            if (terminal != null)
        //            {
        //                terminal.Unbind();
        //            }
        //            if (!terminal.IsDisposed)
        //            {
        //                terminal.Dispose();
        //            }
        //        }
        //    }

        //    return strOut;
        //}

        public static string ExecuteDBCommands(SSHServerInfo objSshInfo, string[] strCommand, string shellPrpt)
        {
            string strOut = string.Empty;
            strOut = ExecuteDBCommands(objSshInfo, strCommand, expectTime, shellPrpt);
            return strOut;
        }

        public static string ExecuteDBCommands(SSHServerInfo objSshInfo, string[] strCommand, int waitTime, string shellPrpt)
        {
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrpt);
            dynamic client = null;
            VirtualTerminal terminal = null;
            string[] _strOuts = new string[strCommand.Length];

            shellPrompt = shellPrpt;
            expectTime = waitTime;

            try
            { 
                int _connectCount = 0;
                while (true)
                {
                    try
                    {
                        client = CreateSSHSession(objSshInfo);

                        if (sshConnOpt == SSHConnFlag.JSession)
                        {
                            if (client.Ssh.Connected)
                            {
                                Enginelog.Info(" PSSH:: ExecuteDBCommands Server: " + objSshInfo.SSHHost.ToString() + " JSession Connected ...!");
                                break;
                            }
                        }
                        else
                        {
                            if (!client.IsDisposed)
                            {
                                Enginelog.Info(" PSSH:: ExecuteDBCommands Server: " + objSshInfo.SSHHost.ToString() + " RSession Connected ...!");
                                break;
                            }
                            //if (client.IsConnected)
                            //{
                            //    Enginelog.Info(" PSSH:: ExecuteDBCommands Server: " + objSshInfo.SSHHost.ToString() + " RSession Connected ...!");
                            //    break;
                            //}
                        }

                        Thread.Sleep(5000);

                    }
                    catch (Exception _ex)
                    {
                        _connectCount++;
                        Thread.Sleep(5000);

                        Enginelog.Error(objSshInfo.SSHHost + " : ExecuteDBCommands Connection Failed _connectCount: " + _connectCount + " : Attempting Reconnect :: Exception Message: " + _ex.Message);
                        if (_connectCount > 4)
                        {
                            Enginelog.Error(objSshInfo.SSHHost + " : ExecuteDBCommands Failed to Connect : Exception Message: " + _ex.Message);
                            throw;
                        }
                    }
                }

                if (client != null)
                {
                    Enginelog.Info(" PSSH:: ExecuteDBCommands calling ProcessSubAuthentication for Server: " + objSshInfo.SSHHost.ToString());
                    if (sshConnOpt == SSHConnFlag.RSession)
                    {
                        terminal = SubAuthentication.ProcessSubAuthentication(objSshInfo, client, shellPrompt);
                    }
                    else if (sshConnOpt == SSHConnFlag.JSession)
                    {
                        client = SubAuthentication.ProcessSubAuthentication(objSshInfo, client, shellPrompt);
                    }
                    Enginelog.Info(" PSSH:: ExecuteDBCommands called ProcessSubAuthentication for Server: " + objSshInfo.SSHHost.ToString());
                    int _errorCount = 0;

                    for (int i = 0; i < strCommand.Length; i++)
                    {
                        if (i == strCommand.Length - 1)
                        {
                            Enginelog.Info(" PSSH: ExecuteDBCommands started on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i]);
                            if (sshConnOpt == SSHConnFlag.RSession)
                            {
                                terminal.Process();
                                terminal.Process();
                                Thread.Sleep(1000);

                                terminal.SendToServer(strCommand[i] + "\n");
                                terminal.Expect(">", expectTime);
                                Thread.Sleep(5000);
                                strOut = terminal.ReceivedData;
                            }
                            else if (sshConnOpt == SSHConnFlag.JSession)
                            {
                                strOut = client.SendWait(strCommand[i], @"SQL>", 1200000);
                                Thread.Sleep(1000);
                            }

                            if (strOut.Contains(strCommand[i]))
                            {
                                strOut = strOut.Replace(strCommand[i] + "\r\n", "");
                            }

                            _strOuts[i] = strOut;
                            Enginelog.Info("PSSH: ExecuteDBCommands Completed on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i] + " Output: " + strOut);

                        }
                        else
                        {
                            Enginelog.Info(" PSSH: ExecuteDBCommands1 started on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i]);
                            if (sshConnOpt == SSHConnFlag.RSession)
                            {
                                terminal.Process();
                                terminal.Process();
                                Thread.Sleep(1000);
                                terminal.SendToServer(strCommand[i] + "\n");
                                terminal.Expect(_reg, expectTime);
                                Thread.Sleep(1000);
                                strOut = terminal.ReceivedData;
                            }
                            else if (sshConnOpt == SSHConnFlag.JSession)
                            {
                                strOut = client.Send(strCommand[i], 600000);
                                Thread.Sleep(1000);
                            }

                            _strOuts[i] = strOut;
                            Enginelog.Info("PSSH: ExecuteDBCommands1 Completed on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i] + " Output: " + strOut);
                        }

                        if (strOut.Contains("sqlplus:  not found"))
                        {
                            if (_errorCount > 3)
                            {
                                Enginelog.Error("PSSH: ExecuteDBCommands :ERROR: sqlplus:  not found on " + objSshInfo.SSHHost);
                                break;
                            }
                            Enginelog.Error("PSSH: ExecuteDBCommands Error: SQLPLUS NOT FOUND on: " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " With Outputs: " + string.Join(",", _strOuts));
                            i = 0;
                            _errorCount++;
                        }
                    }
                }
                else
                {
                    Enginelog.Error("PSSH: client null While ExecuteDBCommands On Server: " + objSshInfo.SSHHost);
                }
            }
            catch (Rebex.Net.SshException sshEx)
            {
                if (sshEx.InnerException != null)
                   Enginelog.Error("InnerException: PSSH: while ExecuteDBCommands on Host " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " Ssh Inner Exception Messege: " + sshEx.Message);
                 
                    Enginelog.Error("Exception: PSSH: while ExecuteDBCommands on Host " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " Ssh Exception Messege: " + sshEx.Message);
                    throw sshEx;
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                  Enginelog.Error("InnerException: PSSH: while ExecuteDBCommands on Host " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " exc Inner Exce Messege: " + exc.Message);
                
                    Enginelog.Error("Exception: PSSH: while ExecuteDBCommands on Host " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " Exc Messege: " + exc.Message);
                    throw exc;
            }
            finally
            {
                if (sshConnOpt == SSHConnFlag.RSession)
                {
                    Enginelog.Info("PSSH: ExecuteDBCommands1 started exit on Server: " + objSshInfo.SSHHost.ToString() );
                    terminal.Process();
                    terminal.SendToServer("exit" + "\n");
                    terminal.Expect(_reg, expectTime);
                    Thread.Sleep(1000);
                    string strOut1 = terminal.ReceivedData;
                    Enginelog.Info("PSSH: ExecuteDBCommands1 exit competed on Server: " + objSshInfo.SSHHost.ToString() + " Command: exit Output: " + strOut1);
                    terminal.Unbind();
                }
                else
                {
                    Enginelog.Info("PSSH: ExecuteDBCommands started exit on Server: " + objSshInfo.SSHHost.ToString());
                    string strOut1 = client.Send("exit");
                    Enginelog.Info("PSSH: ExecuteDBCommands exit competed on Server: " + objSshInfo.SSHHost.ToString() + " Command: exit Output: " + strOut1);
                }

                if (DisconnectAndRemoveSSHSession(client))
                {
                    Enginelog.Info("PSSH: ExecuteDBCommands DisconnectAndRemoveSSHSession Completed on Server: " + objSshInfo.SSHHost.ToString());
                }
            }
            return strOut;
        }

        public static string ExecuteDBCommandsWithPosition(SSHServerInfo objSshInfo, string[] strCommand, string shellPrpt, int dbprptPos)
        {
            string strOut = string.Empty;
            strOut = ExecuteDBCommandsWithPosition(objSshInfo, strCommand, expectTime, shellPrpt, dbprptPos);
            return strOut;
        }

        public static string ExecuteDBCommandsWithPosition(SSHServerInfo objSshInfo, string[] strCommand, int waitTime, string shellPrpt, int dbprptPos)
        {
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrpt);
            dynamic client = null;
            VirtualTerminal terminal = null;
            string[] _strOuts = new string[strCommand.Length];

            shellPrompt = shellPrpt;
            expectTime = waitTime;

            try
            {
                int _connectCount = 0;
                while (true)
                {
                    try
                    {
                        client = CreateSSHSession(objSshInfo);

                        if (sshConnOpt == SSHConnFlag.JSession)
                        {
                            if (client.Ssh.Connected)
                            {
                                Enginelog.Info(" PSSH:: ExecuteDBCommands Server: " + objSshInfo.SSHHost.ToString() + " JSession Connected ...!");
                                break;
                            }
                        }
                        else
                        {
                            if (!client.IsDisposed)
                            {
                                Enginelog.Info(" PSSH:: ExecuteDBCommands Server: " + objSshInfo.SSHHost.ToString() + " RSession Connected ...!");
                                break;
                            }
                        }

                        Thread.Sleep(5000);

                    }
                    catch (Exception _ex)
                    {
                        _connectCount++;
                        Thread.Sleep(5000);

                        Enginelog.Error(objSshInfo.SSHHost + " : ExecuteDBCommands Connection Failed _connectCount: " + _connectCount + " : Attempting Reconnect :: Exception Message: " + _ex.Message);
                        if (_connectCount > 4)
                        {
                            Enginelog.Error(objSshInfo.SSHHost + " : ExecuteDBCommands Failed to Connect : Exception Message: " + _ex.Message);
                            throw;
                        }
                    }
                }

                if (client != null)
                {
                    Enginelog.Info(" PSSH:: ExecuteDBCommands calling ProcessSubAuthentication for Server: " + objSshInfo.SSHHost.ToString());
                    if (sshConnOpt == SSHConnFlag.RSession)
                    {
                        terminal = SubAuthentication.ProcessSubAuthentication(objSshInfo, client, shellPrompt);
                    }
                    else if (sshConnOpt == SSHConnFlag.JSession)
                    {
                        client = SubAuthentication.ProcessSubAuthentication(objSshInfo, client, shellPrompt);
                    }
                    Enginelog.Info(" PSSH:: ExecuteDBCommands called ProcessSubAuthentication for Server: " + objSshInfo.SSHHost.ToString());
                    int _errorCount = 0;

                    for (int i = 0; i < strCommand.Length; i++)
                    {
                        if ((i+1) >= dbprptPos)
                        {
                            Enginelog.Info(" PSSH: ExecuteDBCommands started on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i]);
                            if (sshConnOpt == SSHConnFlag.RSession)
                            {
                                terminal.Process();
                                terminal.Process();
                                Thread.Sleep(1000);

                                terminal.SendToServer(strCommand[i] + "\n");
                                terminal.Expect(">", expectTime);
                                Thread.Sleep(5000);
                                strOut = terminal.ReceivedData;
                            }
                            else if (sshConnOpt == SSHConnFlag.JSession)
                            {
                                strOut = client.SendWait(strCommand[i], @"SQL>", 1200000);
                                Thread.Sleep(1000);
                            }

                            if (strOut.Contains(strCommand[i]))
                            {
                                strOut = strOut.Replace(strCommand[i] + "\r\n", "");
                            }

                            _strOuts[i] = strOut;
                            Enginelog.Info("PSSH: ExecuteDBCommands Completed on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i] + " Output: " + strOut);

                        }
                        else
                        {
                            Enginelog.Info(" PSSH: ExecuteDBCommands1 started on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i]);
                            if (sshConnOpt == SSHConnFlag.RSession)
                            {
                                terminal.Process();
                                terminal.Process();
                                Thread.Sleep(1000);
                                terminal.SendToServer(strCommand[i] + "\n");
                                terminal.Expect(_reg, expectTime);
                                Thread.Sleep(1000);
                                strOut = terminal.ReceivedData;
                            }
                            else if (sshConnOpt == SSHConnFlag.JSession)
                            {
                                strOut = client.Send(strCommand[i], 600000);
                                Thread.Sleep(1000);
                            }

                            _strOuts[i] = strOut;
                            Enginelog.Info("PSSH: ExecuteDBCommands1 Completed on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i] + " Output: " + strOut);
                        }

                        if (strOut.Contains("sqlplus:  not found"))
                        {
                            if (_errorCount > 3)
                            {
                                Enginelog.Error("PSSH: ExecuteDBCommands :ERROR: sqlplus:  not found on " + objSshInfo.SSHHost);
                                break;
                            }
                            Enginelog.Error("PSSH: ExecuteDBCommands Error: SQLPLUS NOT FOUND on: " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " With Outputs: " + string.Join(",", _strOuts));
                            i = 0;
                            _errorCount++;
                        }
                    }
                }
                else
                {
                    Enginelog.Error("PSSH: client null While ExecuteDBCommands On Server: " + objSshInfo.SSHHost);
                }
            }
            catch (Rebex.Net.SshException sshEx)
            {
                if (sshEx.InnerException != null)
                    Enginelog.Error("InnerException: PSSH: while ExecuteDBCommands on Host " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " Ssh Inner Exception Messege: " + sshEx.Message);

                Enginelog.Error("Exception: PSSH: while ExecuteDBCommands on Host " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " Ssh Exception Messege: " + sshEx.Message);
                throw sshEx;
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    Enginelog.Error("InnerException: PSSH: while ExecuteDBCommands on Host " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " exc Inner Exce Messege: " + exc.Message);

                Enginelog.Error("Exception: PSSH: while ExecuteDBCommands on Host " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " Exc Messege: " + exc.Message);
                throw exc;
            }
            finally
            {
                if (sshConnOpt == SSHConnFlag.RSession)
                {
                    Enginelog.Info("PSSH: ExecuteDBCommands1 started exit on Server: " + objSshInfo.SSHHost.ToString());
                    terminal.Process();
                    terminal.SendToServer("exit" + "\n");
                    terminal.Expect(_reg, expectTime);
                    Thread.Sleep(1000);
                    string strOut1 = terminal.ReceivedData;
                    Enginelog.Info("PSSH: ExecuteDBCommands1 exit competed on Server: " + objSshInfo.SSHHost.ToString() + " Command: exit Output: " + strOut1);
                    terminal.Unbind();
                }
                else
                {
                    Enginelog.Info("PSSH: ExecuteDBCommands started exit on Server: " + objSshInfo.SSHHost.ToString());
                    string strOut1 = client.Send("exit");
                    Enginelog.Info("PSSH: ExecuteDBCommands exit competed on Server: " + objSshInfo.SSHHost.ToString() + " Command: exit Output: " + strOut1);
                }

                if (DisconnectAndRemoveSSHSession(client))
                {
                    Enginelog.Info("PSSH: ExecuteDBCommands DisconnectAndRemoveSSHSession Completed on Server: " + objSshInfo.SSHHost.ToString());
                }
            }
            return strOut;
        }

        public static string ExecuteDBCommandsWithSession(dynamic client, SSHServerInfo objSshInfo, string[] strCommand, string shellPrpt)
        {
            string strOut = string.Empty;
            strOut = ExecuteDBCommandsWithSession(client, objSshInfo, strCommand, expectTime, shellPrpt);
            return strOut;
        }

        public static string ExecuteDBCommandsWithSession(dynamic client, SSHServerInfo objSshInfo, string[] strCommand, int waitTime, string shellPrpt)
        {
            //dynamic client = null;
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrpt);
            
            VirtualTerminal terminal = null;
            string[] _strOuts = new string[strCommand.Length];

            shellPrompt = shellPrpt;
            expectTime = waitTime;

            try
            {
                int _connectCount = 0;                

                if (client != null)
                {
                    Enginelog.Info("PSSH: ExecuteDBCommandsWithSession calling ProcessSubAuthentication");
                    if (sshConnOpt == SSHConnFlag.RSession)
                    {
                        terminal = SubAuthentication.ProcessSubAuthentication(objSshInfo, client, shellPrompt);
                    }
                    else if (sshConnOpt == SSHConnFlag.JSession)
                    {
                        client = SubAuthentication.ProcessSubAuthentication(objSshInfo, client, shellPrompt);
                    }
                    Enginelog.Info("PSSH: ExecuteDBCommandsWithSession called ProcessSubAuthentication for Server: " + objSshInfo.SSHHost.ToString());
                    int _errorCount = 0;

                    for (int i = 0; i < strCommand.Length; i++)
                    {
                        if (i == strCommand.Length - 1)
                        {
                            Enginelog.Info("PSSH: ExecuteDBCommandsWithSession started on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i]);
                            if (sshConnOpt == SSHConnFlag.RSession)
                            {
                                terminal.Process();
                                terminal.Process();
                                Thread.Sleep(1000);

                                terminal.SendToServer(strCommand[i] + "\n");
                                terminal.Expect(">", expectTime);
                                Thread.Sleep(5000);
                                strOut = terminal.ReceivedData;
                            }
                            else if (sshConnOpt == SSHConnFlag.JSession)
                            {
                                strOut = client.SendWait(strCommand[i], @"SQL>", 1200000);
                                Thread.Sleep(1000);
                            }

                            if (strOut.Contains(strCommand[i]))
                            {
                                strOut = strOut.Replace(strCommand[i] + "\r\n", "");
                            }

                            _strOuts[i] = strOut;
                            Enginelog.Info("PSSH: ExecuteDBCommandsWithSession Completed on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i] + " Output: " + strOut);

                        }
                        else
                        {
                            Enginelog.Info("PSSH: ExecuteDBCommandsWithSession started on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i]);
                            if (sshConnOpt == SSHConnFlag.RSession)
                            {
                                terminal.Process();
                                terminal.Process();
                                Thread.Sleep(1000);
                                terminal.SendToServer(strCommand[i] + "\n");
                                terminal.Expect(_reg, expectTime);
                                Thread.Sleep(1000);
                                strOut = terminal.ReceivedData;
                            }
                            else if (sshConnOpt == SSHConnFlag.JSession)
                            {
                                strOut = client.Send(strCommand[i], 600000);
                                Thread.Sleep(1000);
                            }

                            _strOuts[i] = strOut;
                            Enginelog.Info("PSSH: ExecuteDBCommandsWithSession Completed on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i] + " Output: " + strOut);
                        }

                        if (strOut.Contains("sqlplus:  not found"))
                        {
                            if (_errorCount > 3)
                            {
                                Enginelog.Error("PSSH: ExecuteDBCommandsWithSession :ERROR: sqlplus:  not found on " + objSshInfo.SSHHost);
                                break;
                            }
                            Enginelog.Error("PSSH: ExecuteDBCommandsWithSession Error: SQLPLUS NOT FOUND on: " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " With Outputs: " + string.Join(",", _strOuts));
                            i = 0;
                            _errorCount++;
                        }
                    }
                }
                else
                {
                    Enginelog.Error("PSSH: client null While ExecuteDBCommands On Server: " + objSshInfo.SSHHost);
                }
            }
            catch (Rebex.Net.SshException sshEx)
            {
                if (sshEx.InnerException != null)
                    Enginelog.Error("InnerException: PSSH: while ExecuteDBCommands on Host " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " Ssh Inner Exception Messege: " + sshEx.Message);

                Enginelog.Error("Exception: PSSH: while ExecuteDBCommands  on Host " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " Ssh Exception Messege: " + sshEx.Message);
                throw sshEx;
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    Enginelog.Error("InnerException: PSSH(ExecuteDBCommandsWithSession): while ExecuteDBCommands on Host " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " exc Inner Exce Messege: " + exc.Message);

                Enginelog.Error("Exception: PSSH(ExecuteDBCommandsWithSession): while ExecuteDBCommands on Host " + objSshInfo.SSHHost + ": Executed All Commands: " + string.Join(",", strCommand) + " Exc Messege: " + exc.Message);
                throw exc;
            }
            finally
            {
                //if (sshConnOpt == SSHConnFlag.RSession)
                //{
                //    terminal.Unbind();
                //} 
            }
            return strOut;
        }

        public static string ExecuteDBCommandsWithSession(ref dynamic client, string[] strCommand, string shellPrpt)
        {
            string strOut = string.Empty;
            strOut = ExecuteDBCommandsWithSession(ref client, strCommand, expectTime, shellPrpt);
            return strOut;
        }

        public static string ExecuteDBCommandsWithSession(ref dynamic client, string[] strCommand, int waitTime, string shellPrpt)
        {
            //dynamic client = null;
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrpt);

            VirtualTerminal terminal = null;
            string[] _strOuts = new string[strCommand.Length];

            shellPrompt = shellPrpt;
            expectTime = waitTime;

            try
            {
                int _connectCount = 0;

                if (client != null)
                {
                    Enginelog.Info("PSSH: ExecuteDBCommandsWithRefSession calling ProcessSubAuthentication");
                    if (sshConnOpt == SSHConnFlag.RSession)
                    {
                        //terminal = new VirtualTerminal(80, 400);
                        //terminal.Bind(client);
                        terminal = client;

                    }
                    else if (sshConnOpt == SSHConnFlag.JSession)
                    {
                        
                    }
                    
                    int _errorCount = 0;

                    for (int i = 0; i < strCommand.Length; i++)
                    {
                        if (i == strCommand.Length - 1)
                        {
                            Enginelog.Info("PSSH: ExecuteDBCommandsWithRefSession started for Command: " + strCommand[i]);
                            if (sshConnOpt == SSHConnFlag.RSession)
                            {
                                terminal.Process();
                                terminal.Process();
                                Thread.Sleep(1000);

                                terminal.SendToServer(strCommand[i] + "\n");
                                terminal.Expect(">", expectTime);
                                Thread.Sleep(5000);
                                strOut = terminal.ReceivedData;
                            }
                            else if (sshConnOpt == SSHConnFlag.JSession)
                            {
                                strOut = client.SendWait(strCommand[i], @"SQL>", 1200000);
                                Thread.Sleep(1000);
                            }

                            if (strOut.Contains(strCommand[i]))
                            {
                                strOut = strOut.Replace(strCommand[i] + "\r\n", "");
                            }

                            _strOuts[i] = strOut;
                            Enginelog.Info("PSSH: ExecuteDBCommandsWithRefSession Completed for Command: " + strCommand[i] + " Output: " + strOut);

                        }
                        else
                        {
                            Enginelog.Info("PSSH: ExecuteDBCommandsWithRefSession started for Command: " + strCommand[i]);
                            if (sshConnOpt == SSHConnFlag.RSession)
                            {
                                terminal.Process();
                                terminal.Process();
                                Thread.Sleep(1000);
                                terminal.SendToServer(strCommand[i] + "\n");
                                terminal.Expect(_reg, expectTime);
                                Thread.Sleep(1000);
                                strOut = terminal.ReceivedData;
                            }
                            else if (sshConnOpt == SSHConnFlag.JSession)
                            {
                                strOut = client.Send(strCommand[i], 600000);
                                Thread.Sleep(1000);
                            }

                            _strOuts[i] = strOut;
                            Enginelog.Info("PSSH: ExecuteDBCommandsWithRefSession Completed for Command: " + strCommand[i] + " Output: " + strOut);
                        }

                        if (strOut.Contains("sqlplus:  not found"))
                        {
                            if (_errorCount > 3)
                            {
                                Enginelog.Error("PSSH: ExecuteDBCommandsWithRefSession :ERROR: sqlplus:  not found ");
                                break;
                            }
                            Enginelog.Error("PSSH: ExecuteDBCommandsWithRefSession Error: SQLPLUS NOT FOUND: Executed All Commands: " + string.Join(",", strCommand) + " With Outputs: " + string.Join(",", _strOuts));
                            i = 0;
                            _errorCount++;
                        }
                    }
                }
                else
                {
                    Enginelog.Error("PSSH: client null While ExecuteDBCommandsWithRefSession");
                }
            }
            catch (Rebex.Net.SshException sshEx)
            {
                if (sshEx.InnerException != null)
                    Enginelog.Error("InnerException: PSSH: while ExecuteDBCommandsWithRefSession: Executed All Commands: " + string.Join(",", strCommand) + " Ssh Inner Exception Messege: " + sshEx.Message);

                Enginelog.Error("Exception: PSSH: while ExecuteDBCommandsWithRefSession: Executed All Commands: " + string.Join(",", strCommand) + " Ssh Exception Messege: " + sshEx.Message);
                throw sshEx;
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    Enginelog.Error("InnerException: PSSH(ExecuteDBCommandsWithRefSession): Executed All Commands: " + string.Join(",", strCommand) + " exc Inner Exce Messege: " + exc.Message);

                Enginelog.Error("Exception: PSSH(ExecuteDBCommandsWithRefSession): Executed All Commands: " + string.Join(",", strCommand) + " Exc Messege: " + exc.Message);
                throw exc;
            }
            //finally
            //{
            //    if (sshConnOpt == SSHConnFlag.RSession)
            //    {
            //        client = terminal;
            //    } 
            //}
            return strOut;
        }

        public static string ExecuteDBCommandWithSession(dynamic client, string command, string shellPrpt)
        {
            string strOut = string.Empty;
            strOut = ExecuteDBCommandWithSession(client, command, expectTime, shellPrpt);
            return strOut;
        }

        public static string ExecuteDBCommandWithSession(dynamic client, string command, int waitTime, string shellPrpt)
        {
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrpt);

            VirtualTerminal terminal = null;
            
            shellPrompt = shellPrpt;
            expectTime = waitTime;

            try
            {
                if (client != null)
                {                   
                    Enginelog.Info("PSSH: ExecuteDBCommandWithSession started for Command: " + command);
                    if (sshConnOpt == SSHConnFlag.RSession)
                    {
                        terminal = client;
                        //terminal.Bind(client);

                        terminal.Process();
                        terminal.Process();
                        Thread.Sleep(1000);

                        terminal.SendToServer(command + "\n");
                        terminal.Expect(">", expectTime);
                        Thread.Sleep(5000);
                        strOut = terminal.ReceivedData;
                    }
                    else if (sshConnOpt == SSHConnFlag.JSession)
                    {
                        strOut = client.SendWait(command, @"SQL>", 1200000);
                        Thread.Sleep(1000);
                    }
                    Enginelog.Info("PSSH: ExecuteDBCommandWithSession completed for Command: " + command);
                }
                else
                {
                    Enginelog.Error("PSSH: ExecuteDBCommandWithSession: client null While ExecuteDBCommands for command: " + command);
                }
            }
            catch (Rebex.Net.SshException sshEx)
            {
                if (sshEx.InnerException != null)
                    Enginelog.Error("InnerException: PSSH(ExecuteDBCommandWithSession): while ExecuteDBCommands for command: " + command + " : " + sshEx.Message);

                Enginelog.Error("Exception: PSSH(ExecuteDBCommandWithSession): while ExecuteDBCommands for command: " + command + " : " + sshEx.Message);
                throw sshEx;
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    Enginelog.Error("InnerException: PSSH(ExecuteDBCommandsWithSession): while ExecuteDBCommands for command: " + command + " : InnerException: " + exc.Message);

                Enginelog.Error("Exception: PSSH(ExecuteDBCommandsWithSession): while ExecuteDBCommands for command: " + command + " : Exc Messege: " + exc.Message);
                throw exc;
            }
            finally
            {
                //if (sshConnOpt == SSHConnFlag.RSession)
                //{
                //    terminal.Unbind();
                //}
            }
            return strOut;
        }

        public static string ExecuteDBCommandWithSession(dynamic client, string command, int waitTime, string shellPrpt, string[] preExeCmds)
        {
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrpt);

            VirtualTerminal terminal = null;

            shellPrompt = shellPrpt;
            expectTime = waitTime;

            try
            {
                if (client != null)
                {
                    Enginelog.Info("PSSH: ExecuteDBCommandWithSession started for Command: " + command);
                    if (sshConnOpt == SSHConnFlag.RSession)
                    {
                        if (preExeCmds != null && preExeCmds.Count() > 0)
                        {
                            ExecuteDBCommandsWithSession(ref client, preExeCmds, shellPrompt);
                        }

                        terminal = client;
                        //terminal.Bind(client);

                        terminal.Process();
                        terminal.Process();
                        Thread.Sleep(1000);

                        terminal.SendToServer(command + "\n");
                        terminal.Expect(">", expectTime);
                        Thread.Sleep(5000);
                        strOut = terminal.ReceivedData;
                    }
                    else if (sshConnOpt == SSHConnFlag.JSession)
                    {
                        strOut = client.SendWait(command, @"SQL>", 1200000);
                        Thread.Sleep(1000);
                    }
                    Enginelog.Info("PSSH: ExecuteDBCommandWithSession completed for Command: " + command);
                }
                else
                {
                    Enginelog.Error("PSSH: ExecuteDBCommandWithSession: client null While ExecuteDBCommands for command: " + command);
                }
            }
            catch (Rebex.Net.SshException sshEx)
            {
                if (sshEx.InnerException != null)
                    Enginelog.Error("InnerException: PSSH(ExecuteDBCommandWithSession): while ExecuteDBCommands for command: " + command + " : " + sshEx.Message);

                Enginelog.Error("Exception: PSSH(ExecuteDBCommandWithSession): while ExecuteDBCommands for command: " + command + " : " + sshEx.Message);
                throw sshEx;
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    Enginelog.Error("InnerException: PSSH(ExecuteDBCommandsWithSession): while ExecuteDBCommands for command: " + command + " : InnerException: " + exc.Message);

                Enginelog.Error("Exception: PSSH(ExecuteDBCommandsWithSession): while ExecuteDBCommands for command: " + command + " : Exc Messege: " + exc.Message);
                throw exc;
            }
            finally
            {
                //if (sshConnOpt == SSHConnFlag.RSession)
                //{
                //    terminal.Unbind();
                //}
            }
            return strOut;
        }

        public static string ExecuteCommandsWithPassword(SSHServerInfo objSshInfo, string[] strCommand, string shellPrpt, string password, int passwordPos, string passwordPrpt)
        {
            string strOut = string.Empty;
            strOut = ExecuteCommandsWithPassword(objSshInfo, strCommand, expectTime, shellPrpt, password, passwordPos, passwordPrpt);
            return strOut;
        }

        public static string ExecuteCommandsWithPassword(SSHServerInfo objSshInfo, string[] strCommand, int waitTime, string shellPrpt, string password, int passwordPos, string passwordPrpt)
        {
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrpt);
            dynamic client = null;
            VirtualTerminal terminal = null;

            try
            {
                int _connectCount = 0;
                while (true)
                {
                    try
                    {
                        client = CreateSSHSession(objSshInfo);

                        if (sshConnOpt == SSHConnFlag.JSession)
                        {
                            if (client.Ssh.Connected)
                            {
                                Enginelog.Info("PSSH: ExecuteCommandsWithPassword Server: " + objSshInfo.SSHHost.ToString() + " JSession Connected ...!");
                                break;
                            }
                        }
                        else
                        {
                            if (client.IsConnected)
                            {
                                Enginelog.Info("PSSH: ExecuteCommandsWithPassword Server: " + objSshInfo.SSHHost.ToString() + " RSession Connected ...!");
                                break;
                            }
                        }

                        Thread.Sleep(5000);

                    }
                    catch (Exception _ex)
                    {
                        _connectCount++;
                        Thread.Sleep(5000);

                        Enginelog.Error(objSshInfo.SSHHost + " : ExecuteCommandsWithPassword Connection Failed _connectCount: " + _connectCount + " : Attempting Reconnect :: Exception Message: " + _ex.Message);
                        if (_connectCount > 4)
                        {
                            Enginelog.Error(objSshInfo.SSHHost + " : ExecuteCommandsWithPassword Failed to Connect: Exception Message: " + _ex.Message);
                            throw;
                        }
                    }
                }

                if (client != null)
                {
                    Enginelog.Info("PSSH: ExecuteCommandsWithPassword calling ProcessSubAuthentication for Server: " + objSshInfo.SSHHost.ToString());
                    if (sshConnOpt == SSHConnFlag.RSession)
                    {
                        terminal = SubAuthentication.ProcessSubAuthentication(objSshInfo, client, shellPrompt);
                    }
                    else if (sshConnOpt == SSHConnFlag.JSession)
                    {
                        client = SubAuthentication.ProcessSubAuthentication(objSshInfo, client, shellPrompt);
                    }
                    Enginelog.Info("PSSH: ExecuteCommandsWithPassword called ProcessSubAuthentication for Server: " + objSshInfo.SSHHost.ToString());
                    

                    for (int i = 0; i < strCommand.Length; i++)
                    {

                        Enginelog.Info("PSSH: ExecuteCommandsWithPassword started on Server: " + objSshInfo.SSHHost.ToString() + " Command: " + strCommand[i]);
                        if (sshConnOpt == SSHConnFlag.RSession)
                        {
                            terminal.Process();
                            terminal.Process();
                            Thread.Sleep(1000);

                            terminal.SendToServer(strCommand[i] + "\n");

                            if ((i + 1) == passwordPos)
                            {
                                terminal.Expect(passwordPrpt, waitTime);
                                strOut = terminal.ReceivedData;

                                if (!string.IsNullOrEmpty(password))
                                {
                                    terminal.Process();
                                    terminal.SendToServer(password + "\r\n");
                                    terminal.Expect(_reg, waitTime);
                                    strOut = terminal.ReceivedData;
                                }
                            }
                            else
                            {
                                terminal.Expect(_reg, expectTime);
                                Thread.Sleep(5000);
                                strOut = terminal.ReceivedData;
                            }
                        }
                        else if (sshConnOpt == SSHConnFlag.JSession)
                        {
                            if ((i + 1) == passwordPos)
                            {
                                strOut = client.SendWait(strCommand[i], passwordPrpt, false, waitTime);
                                if (!string.IsNullOrEmpty(password))
                                {
                                    strOut = client.SendWait(strCommand[i], _reg, waitTime);
                                }
                            }
                            else
                            {
                                strOut = client.SendWait(strCommand[i], _reg, waitTime);
                            }
                            Thread.Sleep(1000);
                        }

                        if (strOut.Contains(strCommand[i]))
                        {
                            strOut = strOut.Replace(strCommand[i] + "\r\n", "");
                        }
                       
                        Enginelog.Info("PSSH: ExecuteCommandsWithPassword Completed on Server: " + objSshInfo.SSHHost.ToString() + " Output: " + strOut);
                       
                    }
                }
                else
                {
                    Enginelog.Error("PSSH: client null in ExecuteCommandsWithPassword On Server: " + objSshInfo.SSHHost);
                }
            }
            catch (Rebex.Net.SshException sshEx)
            {
                if (sshEx.InnerException != null)
                    Enginelog.Error("InnerException: PSSH: in ExecuteCommandsWithPassword on Host " + objSshInfo.SSHHost + " Ssh Inner Exception Messege: " + sshEx.Message);

                Enginelog.Error("Exception: PSSH: in ExecuteCommandsWithPassword on Host " + objSshInfo.SSHHost + " Ssh Exception Messege: " + sshEx.Message);
                throw sshEx;
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    Enginelog.Error("InnerException: PSSH: in ExecuteCommandsWithPassword on Host " + objSshInfo.SSHHost + " exc Inner Exce Messege: " + exc.Message);

                Enginelog.Error("Exception: PSSH: in ExecuteCommandsWithPassword on Host " + objSshInfo.SSHHost + " Exc Messege: " + exc.Message);
                throw exc;
            }
            finally
            {
                if (sshConnOpt == SSHConnFlag.RSession)
                {
                    Enginelog.Info("PSSH: ExecuteCommandsWithPassword started exit on Server: " + objSshInfo.SSHHost.ToString());
                    terminal.Process();
                    terminal.SendToServer("exit" + "\n");
                    terminal.Expect(_reg, expectTime);
                    Thread.Sleep(1000);
                    string strOut1 = terminal.ReceivedData;
                    Enginelog.Info("PSSH: ExecuteCommandsWithPassword exit competed on Server: " + objSshInfo.SSHHost.ToString() + " Command: exit Output: " + strOut);
                    terminal.Unbind();
                }
                else
                {
                    Enginelog.Info("PSSH: ExecuteCommandsWithPassword started exit on Server: " + objSshInfo.SSHHost.ToString());
                    string strOut1 = client.Send("exit");
                    Enginelog.Info("PSSH: ExecuteCommandsWithPassword exit competed on Server: " + objSshInfo.SSHHost.ToString() + " Command: exit Output: " + strOut);
                }

                if (DisconnectAndRemoveSSHSession(client))
                {
                    Enginelog.Info("PSSH: ExecuteCommandsWithPassword DisconnectAndRemoveSSHSession Completed on Server: " + objSshInfo.SSHHost.ToString());
                }
            }

            shellPrompt = shellPrpt;
            expectTime = waitTime;

            return strOut;
        }

        public static string ExecuteCommandsWithSessionAndPassword(dynamic client, string[] strCommand, string shellPrpt, string password, int passwordPos, string passwordPrpt)
        {
            string strOut = string.Empty;
            strOut = ExecuteCommandsWithSessionAndPassword(client, strCommand, expectTime, shellPrpt, password, passwordPos, passwordPrpt);
            return strOut;
        }

        public static string ExecuteCommandsWithSessionAndPassword(dynamic client, string[] strCommand, int waitTime, string shellPrpt, string password, int passwordPos, string passwordPrpt)
        {
            string strOut = string.Empty;
            Regex _reg = new Regex(shellPrpt);
            VirtualTerminal terminal = null;
            try
            {
                if (client != null)
                {
                    for (int i = 0; i < strCommand.Length; i++)
                    {

                        Enginelog.Info("PSSH: ExecuteCommandsWithSessionAndPassword: Command: " + strCommand[i]);
                        if (sshConnOpt == SSHConnFlag.RSession)
                        {
                            terminal = client;

                            terminal.Process();
                            terminal.Process();
                            Thread.Sleep(1000);

                            terminal.SendToServer(strCommand[i] + "\n");

                            if ((i + 1) == passwordPos)
                            {
                                terminal.Expect(passwordPrpt, waitTime);
                                strOut = terminal.ReceivedData;

                                if (!string.IsNullOrEmpty(password))
                                {
                                    terminal.Process();
                                    terminal.SendToServer(password + "\r\n");
                                    terminal.Expect(_reg, waitTime);
                                    strOut = terminal.ReceivedData;
                                }
                            }
                            else
                            {
                                terminal.Expect(_reg, expectTime);
                                Thread.Sleep(5000);
                                strOut = terminal.ReceivedData;
                            }
                        }
                        else if (sshConnOpt == SSHConnFlag.JSession)
                        {
                            if ((i + 1) == passwordPos)
                            {
                                strOut = client.SendWait(strCommand[i], passwordPrpt, false, waitTime);
                                if (!string.IsNullOrEmpty(password))
                                {
                                    strOut = client.SendWait(strCommand[i], _reg, waitTime);
                                }
                            }
                            else
                            {
                                strOut = client.SendWait(strCommand[i], _reg, waitTime);
                            }
                            Thread.Sleep(1000);
                        }

                        if (strOut.Contains(strCommand[i]))
                        {
                            strOut = strOut.Replace(strCommand[i] + "\r\n", "");
                        }

                        Enginelog.Info("PSSH: ExecuteCommandsWithSessionAndPassword: Output: " + strOut);

                    }
                }
            }
            catch (Rebex.Net.SshException sshEx)
            {
                if (sshEx.InnerException != null)
                    Enginelog.Error("InnerException: PSSH: in ExecuteCommandsWithSessionAndPassword Ssh InnerException: " + sshEx.InnerException.Message);

                Enginelog.Error("Exception: PSSH: in ExecuteCommandsWithSessionAndPassword - Ssh Exception: " + sshEx.Message);
                throw sshEx;
            }
            catch (Exception exc)
            {
                if (exc.InnerException != null)
                    Enginelog.Error("InnerException: PSSH: in ExecuteCommandsWithSessionAndPassword - InnerException: " + exc.InnerException.Message);

                Enginelog.Error("Exception: PSSH: in ExecuteCommandsWithSessionAndPassword - Exception: " + exc.Message);
                throw exc;
            }
            finally
            {

            }

            shellPrompt = shellPrpt;
            expectTime = waitTime;

            return strOut;
        }


        #endregion Common Execution methods - PSSH

        #endregion Session - PSSH

    }

    #endregion PSSH

}
